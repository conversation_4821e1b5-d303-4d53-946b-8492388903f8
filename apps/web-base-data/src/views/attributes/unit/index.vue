<script setup lang="ts">
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { UnitInfo } from '#/api';

import { ref, unref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { defineFormOptions } from '@vben/utils';

import { Modal as AModal, message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  addAttributeUnitApi,
  deleteAttributeUnitApi,
  disableAttributeUnitApi,
  enableAttributeUnitApi,
  getAttributeUnitPageList,
  updateAttributeUnitApi,
} from '#/api';

import UnitForm from './components/unit-form.vue';

const formOptions = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'unitCode',
      label: '计量单位编码',
    },
    {
      component: 'Input',
      fieldName: 'unitName',
      label: '计量单位名称',
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '计量单位状态',
      componentProps: {
        options: [
          { label: '启用', value: 1 },
          { label: '禁用', value: 0 },
        ],
      },
    },
  ],
  // 按下回车时是否提交表单
  submitOnEnter: true,
  commonConfig: {
    colon: false,
  },
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'checkbox', width: '60px' },
    { field: 'unitCode', title: '计量单位编码' },
    { field: 'unitName', title: '计量单位名称' },
    // { field: 'remarks', title: '备注' },
    {
      field: 'status',
      title: '状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'baseEnableType',
        },
      },
    },
    // {
    //   field: 'action',
    //   title: '操作',
    //   fixed: 'right',
    //   width: 220,
    //   slots: { default: 'action' },
    // },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async (
        { page }: { page: { currentPage: number; pageSize: number } },
        formValues: { categoryCode: string; categoryName: string; status: string },
      ) => {
        return await getAttributeUnitPageList({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbar-tools',
    },
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const unitFormRef = ref();
const modalTitle = ref('新增单位');
const currentUnit = ref({});

const [Modal, modalApi] = useVbenModal({
  onOpened: async () => {
    unitFormRef.value.init(currentUnit.value);
  },
  onConfirm: async () => {
    currentUnit.value = await unitFormRef.value.submit();
    let api = addAttributeUnitApi;
    if (currentUnit.value.id) {
      api = updateAttributeUnitApi;
    }
    await api(unref(currentUnit) as UnitInfo);
    message.success($t('base.resSuccess'));
    await modalApi.close();
    await gridApi.reload();
  },
});

const addUnit = () => {
  modalTitle.value = '新增单位';
  currentUnit.value = {};
  modalApi.open();
};

const editUnit = (row: UnitInfo) => {
  modalTitle.value = '编辑单位';
  currentUnit.value = { ...row };
  modalApi.open();
};
const enableRows = () => {
  const res = gridApi.grid.getCheckboxRecords(true);
  if (res.length === 0) {
    message.error('请选择数据');
    return false;
  }
  const pidList = res.map((item) => item.id);
  AModal.confirm({
    title: '确认启用',
    content: '确认启用此计量单位？',
    onOk: async () => {
      await enableAttributeUnitApi(pidList);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};

const disenableRows = () => {
  const res = gridApi.grid.getCheckboxRecords(true);
  if (res.length === 0) {
    message.error('请选择数据');
    return false;
  }
  const pidList = res.map((item) => item.id);
  AModal.confirm({
    title: '确认禁用',
    content: '确认禁用此计量单位？',
    onOk: async () => {
      await disableAttributeUnitApi(pidList);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};

const delRows = () => {
  const res = gridApi.grid.getCheckboxRecords(true);
  if (res.length === 0) {
    message.error('请选择数据');
    return false;
  }
  const pidList = res.map((item) => item.id);
  AModal.confirm({
    title: '确认删除',
    content: '确认删除此计量单位？',
    onOk: async () => {
      await deleteAttributeUnitApi(pidList);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <a-button class="mr-2" type="primary" @click="addUnit">新增</a-button>
        <a-button class="mr-2" type="primary" @click="enableRows">启用</a-button>
        <a-button class="mr-2" danger type="primary" @click="disenableRows">禁用</a-button>
        <a-button class="mr-2" danger type="primary" @click="delRows">删除</a-button>
      </template>
      <!--<template #action="{ row }">-->
      <!--  <a-typography-link @click="editUnit(row)">编辑</a-typography-link>-->
      <!--</template>-->
    </Grid>

    <Modal :title="modalTitle">
      <UnitForm ref="unitFormRef" />
    </Modal>
  </Page>
</template>

<style></style>
