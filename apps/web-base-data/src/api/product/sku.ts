import { requestClient } from '#/api/request';

/**
 * SkuRequest，商品SKU
 */
export interface SkuRequest {
  // 商品条码
  barCode?: string;
  // 主键
  id?: number;
  // 图片url
  imgUrl?: string;
  // 第三方编码
  partyCode?: string;
  // sku编码
  skuCode?: string;
  // sku名称
  skuName?: string;
  // sku规格属性列表
  skuSpecList?: SkuSpecRequest[];
  // 规格属性JSON
  specJson?: string;
  // spu主键
  spuId?: number;
  // 状态：1=已启用，0=已禁用
  status?: number;
  // 版本号
  version?: number;
  [property: string]: any;
}

/**
 * SkuSpecRequest，商品SKU
 */
export interface SkuSpecRequest {
  // 主键
  id?: number;
  // SKU ID（外键）
  skuId?: number;
  // 规格名
  specName?: string;
  // 规格值
  specValue?: string;
  // SPU规格ID（外键）
  spuSpecId?: number;
  // 版本号
  version?: number;
  [property: string]: any;
}

export async function editSkuApi(data: SkuRequest) {
  return requestClient.post('/base/product/sku/update', data);
}
export async function enableSkuApi(data: number[]) {
  return requestClient.post('/base/product/sku/enable', data);
}
export async function disableSkuApi(data: number[]) {
  return requestClient.post('/base/product/sku/disable', data);
}
export async function deleteSkuApi(data: number[]) {
  return requestClient.post('/base/product/sku/delete', data);
}
