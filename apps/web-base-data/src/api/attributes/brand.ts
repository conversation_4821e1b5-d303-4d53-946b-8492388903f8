import type { BaseDataParams, PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface BrandInfo extends BaseDataParams {
  // 品牌名称
  brandName: string;
  // 备注
  remarks: string;
}

export async function getAttributeBrandPageList(params: PageListParams) {
  return requestClient.get('/base/brand/page', { params });
}

export async function addAttributeBrandApi(data: BrandInfo) {
  return requestClient.post('/base/brand/add', data);
}

export async function updateAttributeBrandApi(data: BrandInfo) {
  return requestClient.post('/base/brand/update', data);
}

export async function deleteAttributeBrandApi(ids: number[]) {
  return requestClient.post('/base/brand/delete', ids);
}

export async function enableAttributeBrandApi(ids: number[]) {
  return requestClient.post('/base/brand/enable', ids);
}

export async function disableAttributeBrandApi(ids: number[]) {
  return requestClient.post('/base/brand/disable', ids);
}

export async function getAttributeBrandList(params: PageListParams) {
  return requestClient.get('/base/brand/list', { params });
}
