import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

/**
 * MeasureUnitAddRequest，计量单位
 */
export interface MeasureUnitInfo {
  // 备注
  remarks?: string;
  // 计量单位编码
  unitCode: string;
  // 计量单位名称
  unitName: string;
  [property: string]: any;
}

export async function getAttributeUnitPageList(params: PageListParams) {
  return requestClient.get('/base/product/unit/page', { params });
}
export async function getAttributeUnitList(params: PageListParams) {
  return requestClient.get('/base/product/unit/list', { params });
}
export async function addAttributeUnitApi(data: MeasureUnitInfo) {
  return requestClient.post('/base/product/unit/add', data);
}
export async function updateAttributeUnitApi(data: MeasureUnitInfo) {
  return requestClient.post('/base/product/unit/update', data);
}
export async function deleteAttributeUnitApi(ids: number[]) {
  return requestClient.post('/base/product/unit/delete', ids);
}
export async function enableAttributeUnitApi(ids: number[]) {
  return requestClient.post('/base/product/unit/enable', ids);
}
export async function disableAttributeUnitApi(ids: number[]) {
  return requestClient.post('/base/product/unit/disable', ids);
}
