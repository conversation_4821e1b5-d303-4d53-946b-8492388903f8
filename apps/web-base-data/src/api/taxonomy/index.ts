import type { BaseDataParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface TaxonomyInfo extends BaseDataParams {
  pid?: number;
  // 上级类别id(-1顶层，只有一个)
  parentId: number;
  // 分类名称
  categoryName: string;
  // 排序
  sort?: number;
  // 备注
  remarks?: string;
  children?: TaxonomyInfo[];
}
export async function getTaxonomyTreeListApi(params?: { categoryCode: string; categoryName: string; status: string }) {
  return requestClient.get('/base/product/category/tree', { params });
}
export async function getTaxonomyListApi(params?: { categoryCode: string; categoryName: string; status: string }) {
  return requestClient.get('/base/product/category/list', { params });
}
export async function addTaxonomyApi(data: TaxonomyInfo) {
  return requestClient.post('/base/product/category/add', data);
}
export async function updateTaxonomyApi(data: TaxonomyInfo) {
  return requestClient.post('/base/product/category/update', data);
}
export async function saveAttributeCategoryApi(data: TaxonomyInfo) {
  return requestClient.post('/base/product/category/extension/save', data);
}
export async function getTaxonomyAttrInfoApi(params: { categoryId: number }) {
  return requestClient.get('/base/product/category/extension/info', { params });
}
export async function getTaxonomyAttrNameListApi(params: { categoryIds: string }) {
  return requestClient.get('/base/product/category/attr/name/list', { params });
}
